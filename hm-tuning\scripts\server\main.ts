/// <reference types="@citizenfx/server" />

import { VehicleModData } from '../shared/types';

/**
 * Server-side vehicle tuning management
 * Handles saving/loading vehicle modifications to database
 */

interface SavedVehicleMods {
    plate: string;
    mods: Record<string, number>;
    performance: {
        engine: number;
        brakes: number;
        transmission: number;
        suspension: number;
        armor: number;
        turbo: boolean;
        nitrous: boolean;
    };
}

// In-memory storage for vehicle mods (replace with database in production)
const vehicleModsStorage: Map<string, SavedVehicleMods> = new Map();

/**
 * Save vehicle modifications
 */
onNet('hm-tuning:saveVehicleMods', (vehicleData: VehicleModData, plate: string) => {
    const source = global.source;

    console.log(`[hm-tuning] Saving mods for vehicle ${plate} from player ${source}`);

    // Convert mods to a simple format for storage
    const modsToSave: Record<string, number> = {};

    Object.entries(vehicleData.mods).forEach(([modType, modArray]) => {
        const installedMod = modArray.find(mod => mod.isInstalled);
        if (installedMod) {
            modsToSave[modType] = installedMod.modIndex;
        }
    });

    const savedData: SavedVehicleMods = {
        plate: plate,
        mods: modsToSave,
        performance: vehicleData.performance
    };

    // Save to storage (replace with database call)
    vehicleModsStorage.set(plate, savedData);

    console.log(`[hm-tuning] Saved mods for vehicle ${plate}:`, savedData);

    // Confirm save to client
    emitNet('hm-tuning:modsSaved', source, { success: true, plate: plate });
});

/**
 * Load vehicle modifications
 */
onNet('hm-tuning:loadVehicleMods', (plate: string) => {
    const source = global.source;

    console.log(`[hm-tuning] Loading mods for vehicle ${plate} for player ${source}`);

    const savedMods = vehicleModsStorage.get(plate);

    if (savedMods) {
        console.log(`[hm-tuning] Found saved mods for vehicle ${plate}:`, savedMods);
        emitNet('hm-tuning:modsLoaded', source, savedMods);
    } else {
        console.log(`[hm-tuning] No saved mods found for vehicle ${plate}`);
        emitNet('hm-tuning:modsLoaded', source, null);
    }
});

/**
 * Get all saved vehicles for a player (for garage integration)
 */
onNet('hm-tuning:getPlayerVehicles', () => {
    const source = global.source;

    // In a real implementation, you'd query the database for player's vehicles
    // For now, return all saved vehicles
    const allVehicles = Array.from(vehicleModsStorage.values());

    emitNet('hm-tuning:playerVehicles', source, allVehicles);
});

/**
 * Apply saved mods to a vehicle (called when spawning a vehicle)
 */
export function applySavedMods(plate: string): SavedVehicleMods | null {
    return vehicleModsStorage.get(plate) || null;
}

// Export for other resources to use
global.exports('applySavedMods', applySavedMods);
global.exports('saveVehicleMods', (plate: string, mods: SavedVehicleMods) => {
    vehicleModsStorage.set(plate, mods);
});

console.log('[hm-tuning] Server initialized - Vehicle tuning system ready');