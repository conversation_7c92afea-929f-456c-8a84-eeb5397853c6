import { useEffect } from 'react';
import { useTuningStore } from '../store/tuningStore';

// NUI message handler hook
export const useNUI = () => {
  const handleNUIMessage = useTuningStore(state => state.handleNUIMessage);

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      try {
        const message = event.data;
        if (message && typeof message === 'object') {
          handleNUIMessage(message);
        }
      } catch (error) {
        console.error('[Tuning UI] Error handling NUI message:', error);
      }
    };

    // Listen for NUI messages
    window.addEventListener('message', handleMessage);

    // Cleanup
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [handleNUIMessage]);

  // Function to send messages back to FiveM client
  const sendNUIMessage = (type: string, data?: any) => {
    fetch(`https://hm-tuning/${type}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data || {}),
    }).catch(error => {
      console.error('[Tuning UI] Error sending NUI message:', error);
    });
  };

  return { sendNUIMessage };
};

// Keyboard handler hook
export const useKeyboard = () => {
  const setVisible = useTuningStore(state => state.setVisible);
  const { sendNUIMessage } = useNUI();

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // ESC key to close UI
      if (event.key === 'Escape') {
        setVisible(false);
        sendNUIMessage('CLOSE_UI');
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [setVisible, sendNUIMessage]);
};
