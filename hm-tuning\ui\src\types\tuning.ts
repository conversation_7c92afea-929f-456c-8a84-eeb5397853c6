// Types for the tuning UI - matches the shared types from the client
export enum VehicleModType {
    SPOILER = 0,
    BUMPER_F = 1,
    BUMPER_R = 2,
    SKIRT = 3,
    EXHAUST = 4,
    CHASSIS = 5,
    GRILL = 6,
    BONNET = 7,
    WING_L = 8,
    WING_R = 9,
    ROOF = 10,
    ENGINE = 11,
    BRAKES = 12,
    GEARBOX = 13,
    HORN = 14,
    SUSPENSION = 15,
    ARMOUR = 16,
    NITROUS = 17,
    TURBO = 18,
    SUBWOOFER = 19,
    TYRE_SMOKE = 20,
    HYDRAULICS = 21,
    XENON_LIGHTS = 22,
    WHEELS = 23,
    WHEELS_REAR_OR_HYDRAULICS = 24,
    PLTHOLDER = 25,
    PLTVANITY = 26,
    INTERIOR1 = 27,
    INTERIOR2 = 28,
    INTERIOR3 = 29,
    INTERIOR4 = 30,
    INTERIOR5 = 31,
    SEATS = 32,
    STEERING = 33,
    KNOB = 34,
    PLAQUE = 35,
    ICE = 36,
    TRUNK = 37,
    HYDRO = 38,
    ENGINEBAY1 = 39,
    <PERSON>NGINEBAY2 = 40,
    ENGINEBAY3 = 41,
    CHASSIS2 = 42,
    CHASSIS3 = 43,
    CHASSIS4 = 44,
    CHASSIS5 = 45,
    DOOR_L = 46,
    DOOR_R = 47,
    LIVERY_MOD = 48,
    LIGHTBAR = 49
}

export enum ModCategory {
    BODY = 'body',
    PERFORMANCE = 'performance',
    EFFECTS = 'effects',
    STANCE = 'stance',
    INTERIOR = 'interior'
}

export interface VehicleMod {
    modType: VehicleModType;
    modIndex: number;
    name: string;
    category: ModCategory;
    isInstalled: boolean;
    isStock: boolean;
}

export interface VehicleModData {
    vehicleModel: string;
    vehicleHash: number;
    mods: Record<VehicleModType, VehicleMod[]>;
    performance: {
        engine: number;
        brakes: number;
        transmission: number;
        suspension: number;
        armor: number;
        turbo: boolean;
        nitrous: boolean;
    };
}

// UI Category configuration
export interface UICategory {
    id: string;
    name: string;
    category: ModCategory;
    icon: React.ReactNode;
    subCategories: UISubCategory[];
}

// Sub-category within a main category
export interface UISubCategory {
    id: string;
    name: string;
    modType: VehicleModType;
    mods: VehicleMod[];
}

// Navigation state
export enum NavigationLevel {
    MAIN_CATEGORIES = 'main_categories',
    SUB_CATEGORIES = 'sub_categories',
    MODS = 'mods'
}

// FiveM NUI message types
export interface NUIMessage {
    type: string;
    data: any;
}

export interface TuningState {
    isVisible: boolean;
    vehicleData: VehicleModData | null;
    categories: UICategory[];
    selectedCategory: string | null;
    selectedSubCategory: string | null;
    currentLevel: NavigationLevel;
    loading: boolean;
}
