// Re-export all shared types for UI consistency
export {
    VehicleModType,
    ModCategory,
    NavigationLevel,
    MOD_CATEGORIES,
    PERFORMANCE_MODS
} from '../../../scripts/shared/types';

export type {
    VehicleMod,
    VehicleModData,
    UICategory,
    UISubCategory,
    TuningState,
    NUIMessage
} from '../../../scripts/shared/types';

// UI-specific interface that extends the shared UICategory with React icon
import type { UICategory } from '../../../scripts/shared/types';
export interface UIReactCategory extends Omit<UICategory, 'icon'> {
    icon: React.ReactNode;
}
