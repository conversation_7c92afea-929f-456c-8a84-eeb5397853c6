/// <reference types="@citizenfx/client" />

import { VehicleModType, ModCategory, VehicleMod, VehicleModData, MOD_CATEGORIES } from '../shared/types';

/**
 * Get all available modifications for a vehicle dynamically
 * This function queries the game engine for available mods - NOT hardcoded!
 */
export function getAvailableVehicleMods(vehicle: number): VehicleModData | null {
    if (!DoesEntityExist(vehicle)) {
        console.error('[hm-tuning] Vehicle does not exist');
        return null;
    }

    const vehicleModel = GetEntityModel(vehicle);
    const vehicleHash = vehicleModel;

    console.log(`[hm-tuning] Getting available mods for vehicle model: ${vehicleModel}`);

    const modData: VehicleModData = {
        vehicleModel: GetDisplayNameFromVehicleModel(vehicleModel),
        vehicleHash: vehicleHash,
        mods: {} as Record<VehicleModType, VehicleMod[]>,
        performance: {
            engine: GetVehicleMod(vehicle, VehicleModType.ENGINE),
            brakes: GetVehicleMod(vehicle, VehicleModType.BRAKES),
            transmission: GetVehicleMod(vehicle, VehicleModType.GEARBOX),
            suspension: GetVehicleMod(vehicle, VehicleModType.SUSPENSION),
            armor: GetVehicleMod(vehicle, VehicleModType.ARMOUR),
            turbo: IsToggleModOn(vehicle, VehicleModType.TURBO),
            nitrous: GetVehicleMod(vehicle, VehicleModType.NITROUS) !== -1
        }
    };

    // Iterate through all mod types
    for (const modTypeKey in VehicleModType) {
        const modType = VehicleModType[modTypeKey as keyof typeof VehicleModType];

        if (typeof modType !== 'number') continue;

        // Get number of available mods for this type
        const numMods = GetNumVehicleMods(vehicle, modType);

        if (numMods > 0) {
            const mods: VehicleMod[] = [];
            const currentMod = GetVehicleMod(vehicle, modType);

            // Add stock option
            mods.push({
                modType: modType,
                modIndex: -1,
                name: 'Stock',
                category: MOD_CATEGORIES[modType],
                isInstalled: currentMod === -1,
                isStock: true
            });

            // Add all available mods
            for (let i = 0; i < numMods; i++) {
                const modName = GetModTextLabel(vehicle, modType, i) || `Mod ${i + 1}`;

                mods.push({
                    modType: modType,
                    modIndex: i,
                    name: modName,
                    category: MOD_CATEGORIES[modType],
                    isInstalled: currentMod === i,
                    isStock: false
                });
            }

            modData.mods[modType] = mods;

            console.log(`[hm-tuning] Found ${numMods} mods for type ${modType} (${VehicleModType[modType]})`);
        }
    }

    return modData;
}

/**
 * Get mods organized by category for UI display
 */
export function getModsByCategory(vehicleModData: VehicleModData): Record<ModCategory, VehicleMod[]> {
    const categorizedMods: Record<ModCategory, VehicleMod[]> = {
        [ModCategory.BODY]: [],
        [ModCategory.PERFORMANCE]: [],
        [ModCategory.EFFECTS]: [],
        [ModCategory.STANCE]: [],
        [ModCategory.INTERIOR]: []
    };

    // Organize mods by category
    Object.values(vehicleModData.mods).forEach(modArray => {
        modArray.forEach(mod => {
            categorizedMods[mod.category].push(mod);
        });
    });

    return categorizedMods;
}

/**
 * Apply a modification to a vehicle
 */
export function applyVehicleMod(vehicle: number, modType: VehicleModType, modIndex: number): boolean {
    if (!DoesEntityExist(vehicle)) {
        console.error('[hm-tuning] Vehicle does not exist');
        return false;
    }

    try {
        // Special handling for turbo (toggle mod)
        if (modType === VehicleModType.TURBO) {
            ToggleVehicleMod(vehicle, modType, modIndex > -1);
        } else {
            SetVehicleMod(vehicle, modType, modIndex, false);
        }

        console.log(`[hm-tuning] Applied mod: Type ${modType}, Index ${modIndex}`);
        return true;
    } catch (error) {
        console.error('[hm-tuning] Error applying mod:', error);
        return false;
    }
}

/**
 * Get performance stats for a vehicle
 */
export function getVehiclePerformanceStats(vehicle: number) {
    if (!DoesEntityExist(vehicle)) return null;

    return {
        engine: GetVehicleMod(vehicle, VehicleModType.ENGINE),
        brakes: GetVehicleMod(vehicle, VehicleModType.BRAKES),
        transmission: GetVehicleMod(vehicle, VehicleModType.GEARBOX),
        suspension: GetVehicleMod(vehicle, VehicleModType.SUSPENSION),
        armor: GetVehicleMod(vehicle, VehicleModType.ARMOUR),
        turbo: IsToggleModOn(vehicle, VehicleModType.TURBO),
        nitrous: GetVehicleMod(vehicle, VehicleModType.NITROUS) !== -1,
        // Additional performance metrics
        maxSpeed: GetVehicleEstimatedMaxSpeed(vehicle),
        acceleration: GetVehicleModelAcceleration(GetEntityModel(vehicle)),
        braking: GetVehicleMaxBraking(vehicle),
        traction: GetVehicleMaxTraction(vehicle)
    };
}

// Example usage - get mods for player's current vehicle
RegisterCommand('getmods', () => {
    const playerPed = PlayerPedId();
    const vehicle = GetVehiclePedIsIn(playerPed, false);

    if (vehicle === 0) {
        console.log('[hm-tuning] Player is not in a vehicle');
        return;
    }

    const modData = getAvailableVehicleMods(vehicle);
    if (modData) {
        console.log('[hm-tuning] Available mods:', modData);

        // Organize by category
        const categorizedMods = getModsByCategory(modData);
        console.log('[hm-tuning] Mods by category:', categorizedMods);

        // Get performance stats
        const performanceStats = getVehiclePerformanceStats(vehicle);
        console.log('[hm-tuning] Performance stats:', performanceStats);
    }
}, false);