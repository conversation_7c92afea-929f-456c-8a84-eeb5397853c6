/// <reference types="@citizenfx/client" />

import {
    VehicleModType,
    ModCategory,
    VehicleMod,
    VehicleModData,
    MOD_CATEGORIES
} from '../shared/types';

/**
 * Get all available modifications for a vehicle dynamically
 * This function queries the game engine for available mods - NOT hardcoded!
 */
export function getAvailableVehicleMods(vehicle: number): VehicleModData | null {
    if (!DoesEntityExist(vehicle)) {
        console.error('[hm-tuning] Vehicle does not exist');
        return null;
    }

    const vehicleModel = GetEntityModel(vehicle);
    const vehicleHash = vehicleModel;

    console.log(`[hm-tuning] Getting available mods for vehicle model: ${vehicleModel}`);

    const modData: VehicleModData = {
        vehicleModel: GetDisplayNameFromVehicleModel(vehicleModel),
        vehicleHash: vehicleHash,
        mods: {} as Record<VehicleModType, VehicleMod[]>,
        performance: {
            engine: GetVehicleMod(vehicle, VehicleModType.ENGINE),
            brakes: GetVehicleMod(vehicle, VehicleModType.BRAKES),
            transmission: GetVehicleMod(vehicle, VehicleModType.GEARBOX),
            suspension: GetVehicleMod(vehicle, VehicleModType.SUSPENSION),
            armor: GetVehicleMod(vehicle, VehicleModType.ARMOUR),
            turbo: IsToggleModOn(vehicle, VehicleModType.TURBO),
            nitrous: GetVehicleMod(vehicle, VehicleModType.NITROUS) !== -1
        }
    };

    // Iterate through all mod types
    for (const modTypeKey in VehicleModType) {
        const modType = VehicleModType[modTypeKey as keyof typeof VehicleModType];

        if (typeof modType !== 'number') continue;

        // Get number of available mods for this type
        const numMods = GetNumVehicleMods(vehicle, modType);

        if (numMods > 0) {
            const mods: VehicleMod[] = [];
            const currentMod = GetVehicleMod(vehicle, modType);

            // Add stock option
            mods.push({
                modType: modType,
                modIndex: -1,
                name: 'Stock',
                category: MOD_CATEGORIES[modType],
                isInstalled: currentMod === -1,
                isStock: true
            });

            // Add all available mods
            for (let i = 0; i < numMods; i++) {
                const modName = GetModTextLabel(vehicle, modType, i) || `Mod ${i + 1}`;

                mods.push({
                    modType: modType,
                    modIndex: i,
                    name: modName,
                    category: MOD_CATEGORIES[modType],
                    isInstalled: currentMod === i,
                    isStock: false
                });
            }

            modData.mods[modType] = mods;

            console.log(`[hm-tuning] Found ${numMods} mods for type ${modType} (${VehicleModType[modType]})`);
        }
    }

    return modData;
}

/**
 * Get mods organized by category for UI display
 */
export function getModsByCategory(vehicleModData: VehicleModData): Record<ModCategory, VehicleMod[]> {
    const categorizedMods: Record<ModCategory, VehicleMod[]> = {
        [ModCategory.BODY]: [],
        [ModCategory.PERFORMANCE]: [],
        [ModCategory.EFFECTS]: [],
        [ModCategory.STANCE]: [],
        [ModCategory.INTERIOR]: []
    };

    // Organize mods by category
    Object.values(vehicleModData.mods).forEach(modArray => {
        modArray.forEach(mod => {
            categorizedMods[mod.category].push(mod);
        });
    });

    return categorizedMods;
}

/**
 * Apply a modification to a vehicle
 */
export function applyVehicleMod(vehicle: number, modType: VehicleModType, modIndex: number): boolean {
    if (!DoesEntityExist(vehicle)) {
        console.error('[hm-tuning] Vehicle does not exist');
        return false;
    }

    try {
        // Special handling for turbo (toggle mod)
        if (modType === VehicleModType.TURBO) {
            ToggleVehicleMod(vehicle, modType, modIndex > -1);
        } else {
            SetVehicleMod(vehicle, modType, modIndex, false);
        }

        console.log(`[hm-tuning] Applied mod: Type ${modType}, Index ${modIndex}`);
        return true;
    } catch (error) {
        console.error('[hm-tuning] Error applying mod:', error);
        return false;
    }
}

/**
 * Get performance stats for a vehicle
 */
export function getVehiclePerformanceStats(vehicle: number) {
    if (!DoesEntityExist(vehicle)) return null;

    return {
        engine: GetVehicleMod(vehicle, VehicleModType.ENGINE),
        brakes: GetVehicleMod(vehicle, VehicleModType.BRAKES),
        transmission: GetVehicleMod(vehicle, VehicleModType.GEARBOX),
        suspension: GetVehicleMod(vehicle, VehicleModType.SUSPENSION),
        armor: GetVehicleMod(vehicle, VehicleModType.ARMOUR),
        turbo: IsToggleModOn(vehicle, VehicleModType.TURBO),
        nitrous: GetVehicleMod(vehicle, VehicleModType.NITROUS) !== -1,
        // Additional performance metrics
        maxSpeed: GetVehicleEstimatedMaxSpeed(vehicle),
        acceleration: GetVehicleModelAcceleration(GetEntityModel(vehicle)),
        braking: GetVehicleMaxBraking(vehicle),
        traction: GetVehicleMaxTraction(vehicle)
    };
}

/**
 * UI Integration Functions
 */

// Send message to NUI
function sendNUIMessage(type: string, data?: any) {
    const message = {
        type: type,
        data: data || {}
    };
    console.log('[hm-tuning] Sending NUI message:', JSON.stringify(message));
    SendNuiMessage(JSON.stringify(message));
}

// Show tuning UI
export function showTuningUI() {
    const playerPed = PlayerPedId();
    const vehicle = GetVehiclePedIsIn(playerPed, false);

    if (vehicle === 0) {
        console.log('[hm-tuning] Player is not in a vehicle');
        return;
    }

    // Show UI with loading state
    sendNUIMessage('SHOW_TUNING_UI');
    sendNUIMessage('SET_LOADING', { loading: true });

    // Set NUI focus
    SetNuiFocus(true, true);

    // Get vehicle mod data
    const modData = getAvailableVehicleMods(vehicle);
    if (modData) {
        // Send data to UI
        sendNUIMessage('UPDATE_VEHICLE_DATA', modData);

        console.log('[hm-tuning] ========== TUNING UI OPENED ==========');
        console.log('[hm-tuning] Vehicle:', modData.vehicleModel);
        console.log('[hm-tuning] Categories available:', Object.keys(modData.mods).length);
        console.log('[hm-tuning] Total mods:', Object.values(modData.mods).reduce((total, modArray) => total + modArray.length, 0));
    } else {
        sendNUIMessage('SET_LOADING', { loading: false });
    }
}

// Hide tuning UI
export function hideTuningUI() {
    sendNUIMessage('HIDE_TUNING_UI');
    SetNuiFocus(false, false);
    console.log('[hm-tuning] Tuning UI closed');
}

// NUI Callbacks
RegisterNuiCallbackType('APPLY_MOD');
on('__cfx_nui:APPLY_MOD', (data: any, cb: Function) => {
    const playerPed = PlayerPedId();
    const vehicle = GetVehiclePedIsIn(playerPed, false);

    if (vehicle === 0) {
        console.log('[hm-tuning] No vehicle to apply mod to');
        cb({ success: false, error: 'No vehicle' });
        return;
    }

    const { modType, modIndex, modName } = data;
    console.log(`[hm-tuning] Applying mod: ${modName} (Type: ${modType}, Index: ${modIndex})`);

    const success = applyVehicleMod(vehicle, modType, modIndex);

    if (success) {
        // Update UI with new vehicle data
        const updatedModData = getAvailableVehicleMods(vehicle);
        if (updatedModData) {
            sendNUIMessage('UPDATE_VEHICLE_DATA', updatedModData);
        }

        console.log(`[hm-tuning] Successfully applied mod: ${modName}`);
    } else {
        console.log(`[hm-tuning] Failed to apply mod: ${modName}`);
    }

    cb({ success: success });
});

RegisterNuiCallbackType('CLOSE_UI');
on('__cfx_nui:CLOSE_UI', (_data: any, cb: Function) => {
    hideTuningUI();
    cb({ success: true });
});

RegisterNuiCallbackType('CANCEL_CHANGES');
on('__cfx_nui:CANCEL_CHANGES', (_data: any, cb: Function) => {
    const playerPed = PlayerPedId();
    const vehicle = GetVehiclePedIsIn(playerPed, false);

    if (vehicle === 0) {
        console.log('[hm-tuning] No vehicle to revert changes');
        cb({ success: false, error: 'No vehicle' });
        return;
    }

    console.log('[hm-tuning] Reverting vehicle to original state...');

    // Get the original vehicle data and reapply all mods
    // This would need to be stored when the UI first opens
    // For now, we'll just close the UI - you can enhance this later
    hideTuningUI();

    cb({ success: true });
});

// Commands
RegisterCommand('tuning', () => {
    showTuningUI();
}, false);

RegisterCommand('getmods', () => {
    const playerPed = PlayerPedId();
    const vehicle = GetVehiclePedIsIn(playerPed, false);

    if (vehicle === 0) {
        console.log('[hm-tuning] Player is not in a vehicle');
        return;
    }

    const modData = getAvailableVehicleMods(vehicle);
    if (modData) {
        console.log('[hm-tuning] ========== VEHICLE MOD DATA ==========');
        console.log('[hm-tuning] Vehicle:', modData.vehicleModel);
        console.log('[hm-tuning] Hash:', modData.vehicleHash);
        console.log('[hm-tuning] Available mods:', JSON.stringify(modData.mods, null, 2));

        // Organize by category
        const categorizedMods = getModsByCategory(modData);
        console.log('[hm-tuning] ========== MODS BY CATEGORY ==========');
        console.log('[hm-tuning] Body mods:', JSON.stringify(categorizedMods.body, null, 2));
        console.log('[hm-tuning] Performance mods:', JSON.stringify(categorizedMods.performance, null, 2));
        console.log('[hm-tuning] Effects mods:', JSON.stringify(categorizedMods.effects, null, 2));
        console.log('[hm-tuning] Stance mods:', JSON.stringify(categorizedMods.stance, null, 2));
        console.log('[hm-tuning] Interior mods:', JSON.stringify(categorizedMods.interior, null, 2));

        // Get performance stats
        const performanceStats = getVehiclePerformanceStats(vehicle);
        console.log('[hm-tuning] ========== PERFORMANCE STATS ==========');
        console.log('[hm-tuning] Performance stats:', JSON.stringify(performanceStats, null, 2));

        // Summary
        const totalMods = Object.values(modData.mods).reduce((total, modArray) => total + modArray.length, 0);
        console.log('[hm-tuning] ========== SUMMARY ==========');
        console.log(`[hm-tuning] Total available modifications: ${totalMods}`);
        console.log(`[hm-tuning] Mod categories found: ${Object.keys(modData.mods).length}`);
    }
}, false);

// Test command to show UI immediately for debugging
RegisterCommand('testui', () => {
    console.log('[hm-tuning] Testing UI visibility...');
    sendNUIMessage('SHOW_TUNING_UI');
    SetNuiFocus(true, true);
    console.log('[hm-tuning] UI should now be visible');
}, false);

// Simple test to check if NUI is working
RegisterCommand('testnui', () => {
    console.log('[hm-tuning] Testing basic NUI communication...');
    sendNUIMessage('TEST_MESSAGE', { test: 'Hello from FiveM!' });
    SetNuiFocus(true, true);
}, false);

console.log('[hm-tuning] Client initialized - Use /tuning to open UI, /getmods for console output, or /testui to test UI visibility');