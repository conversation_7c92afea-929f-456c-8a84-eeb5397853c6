<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tuning UI Test</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        iframe {
            width: 100vw;
            height: 100vh;
            border: none;
        }
        .controls {
            position: fixed;
            top: 10px;
            left: 10px;
            z-index: 1000;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
        }
        button {
            margin: 5px;
            padding: 10px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="controls">
        <h3>Tuning UI Test</h3>
        <button onclick="sendMessage('SHOW_TUNING_UI')">Show UI</button>
        <button onclick="sendMessage('HIDE_TUNING_UI')">Hide UI</button>
        <button onclick="sendMockData()">Send Mock Data</button>
        <button onclick="testApplyMod()">Test Apply Mod</button>
    </div>
    
    <iframe id="tuningFrame" src="ui/build/index.html"></iframe>

    <script>
        const frame = document.getElementById('tuningFrame');
        
        function sendMessage(type, data = {}) {
            console.log('Sending message:', type, data);
            frame.contentWindow.postMessage({
                type: type,
                data: data
            }, '*');
        }

        function sendMockData() {
            const mockData = {
                vehicleModel: "ADDER",
                vehicleHash: 3078201489,
                mods: {
                    0: [ // Spoilers
                        { modType: 0, modIndex: -1, name: "Stock", category: "body", isInstalled: true, isStock: true },
                        { modType: 0, modIndex: 0, name: "Carbon Spoiler", category: "body", isInstalled: false, isStock: false },
                        { modType: 0, modIndex: 1, name: "Ducktail Spoiler", category: "body", isInstalled: false, isStock: false }
                    ],
                    11: [ // Engine
                        { modType: 11, modIndex: -1, name: "Stock", category: "performance", isInstalled: false, isStock: true },
                        { modType: 11, modIndex: 0, name: "Level 1", category: "performance", isInstalled: false, isStock: false },
                        { modType: 11, modIndex: 1, name: "Level 2", category: "performance", isInstalled: true, isStock: false },
                        { modType: 11, modIndex: 2, name: "Level 3", category: "performance", isInstalled: false, isStock: false }
                    ],
                    23: [ // Wheels
                        { modType: 23, modIndex: -1, name: "Stock", category: "stance", isInstalled: true, isStock: true },
                        { modType: 23, modIndex: 0, name: "Sport Wheels", category: "stance", isInstalled: false, isStock: false }
                    ]
                },
                performance: {
                    engine: 1,
                    brakes: -1,
                    transmission: -1,
                    suspension: -1,
                    armor: -1,
                    turbo: false,
                    nitrous: false
                }
            };
            
            sendMessage('UPDATE_VEHICLE_DATA', mockData);
        }

        function testApplyMod() {
            // Simulate applying a mod
            const modData = {
                modType: 0,
                modIndex: 1,
                modName: "Ducktail Spoiler"
            };
            
            sendMessage('APPLY_MOD', modData);
        }

        // Listen for messages from the iframe
        window.addEventListener('message', function(event) {
            console.log('Received message from iframe:', event.data);
        });

        // Auto-show UI and send mock data after a short delay
        setTimeout(() => {
            sendMessage('SHOW_TUNING_UI');
            setTimeout(() => {
                sendMockData();
            }, 500);
        }, 1000);
    </script>
</body>
</html>
