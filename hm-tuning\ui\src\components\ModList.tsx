import React from 'react';
import { VehicleMod } from '../types/tuning';

interface ModListProps {
  mods: VehicleMod[];
  onModSelect: (mod: VehicleMod) => void;
}

export const ModList: React.FC<ModListProps> = ({ mods, onModSelect }) => {
  if (mods.length === 0) {
    return (
      <div className="flex items-center justify-center h-full text-gray-400">
        <div className="text-center">
          <div className="text-4xl mb-4">🔧</div>
          <div className="text-lg">No modifications available</div>
          <div className="text-sm opacity-75">This vehicle doesn't support mods in this category</div>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 p-6 h-full overflow-y-auto">
      {mods.map((mod, index) => (
        <div
          key={`${mod.modType}-${mod.modIndex}`}
          onClick={() => onModSelect(mod)}
          className={`
            relative cursor-pointer transition-all duration-300 rounded-lg overflow-hidden
            ${mod.isInstalled 
              ? 'bg-white text-black border-2 border-white shadow-lg transform scale-105' 
              : 'bg-gray-800 text-white border border-gray-600 hover:bg-gray-700 hover:border-gray-500'
            }
          `}
          style={{ minHeight: '120px' }}
        >
          {/* Mod preview area - could show 3D preview in future */}
          <div className="h-16 bg-gradient-to-br from-gray-600 to-gray-800 flex items-center justify-center">
            {mod.isStock ? (
              <div className="text-2xl">📦</div>
            ) : (
              <div className="text-2xl">🔧</div>
            )}
          </div>

          {/* Mod info */}
          <div className="p-3">
            <div className={`text-sm font-bold mb-1 ${mod.isInstalled ? 'text-black' : 'text-white'}`}>
              {mod.name}
            </div>
            
            <div className={`text-xs opacity-75 ${mod.isInstalled ? 'text-black' : 'text-gray-300'}`}>
              {mod.isStock ? 'Stock' : `Level ${mod.modIndex + 1}`}
            </div>

            {/* Installation indicator */}
            {mod.isInstalled && (
              <div className="absolute top-2 right-2">
                <div className="w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
              </div>
            )}

            {/* Stock indicator */}
            {mod.isStock && !mod.isInstalled && (
              <div className="absolute top-2 left-2">
                <div className="text-xs bg-blue-600 text-white px-2 py-1 rounded">
                  STOCK
                </div>
              </div>
            )}
          </div>

          {/* Hover effect */}
          <div className={`
            absolute inset-0 opacity-0 transition-opacity duration-300
            ${!mod.isInstalled ? 'hover:opacity-10 bg-white' : ''}
          `}></div>
        </div>
      ))}
    </div>
  );
};

export default ModList;
