import { useState, useRef, useEffect } from "react";

// FiveM Vehicle Modification Categories (matching GTA vehicle mod types)
const categories = [
  {
    id: "spoilers",
    name: "SPOILERS",
    modType: 0,
    icon: (
      <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
        <path d="M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.22.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 16c-.83 0-1.5-.67-1.5-1.5S5.67 13 6.5 13s1.5.67 1.5 1.5S7.33 16 6.5 16zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM5 11l1.5-4.5h11L19 11H5z"/>
      </svg>
    )
  },
  {
    id: "front_bumper",
    name: "FRONT BUMPER",
    modType: 1,
    icon: (
      <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
        <path d="M19 15l-6 6h-2l-6-6h2l5 5 5-5h2zm0-6l-6-6h-2L5 9h2l5-5 5 5h2z"/>
      </svg>
    )
  },
  {
    id: "rear_bumper",
    name: "REAR BUMPER",
    modType: 2,
    icon: (
      <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
        <path d="M5 9l6-6h2l6 6h-2l-5-5-5 5H5zm0 6l6 6h2l6-6h-2l-5 5-5-5H5z"/>
      </svg>
    )
  },
  {
    id: "side_skirt",
    name: "SIDE SKIRT",
    modType: 3,
    icon: (
      <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
        <path d="M3 12h18v2H3v-2zm0-4h18v2H3V8zm0 8h18v2H3v-2z"/>
      </svg>
    )
  },
  {
    id: "exhaust",
    name: "EXHAUST",
    modType: 4,
    icon: (
      <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
        <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
      </svg>
    )
  },
  {
    id: "frame",
    name: "FRAME",
    modType: 5,
    icon: (
      <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
        <path d="M19 7h-3V6a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v1H7a1 1 0 0 0-1 1v11a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V8a1 1 0 0 0-1-1zM11 6h2v1h-2V6zm7 12H6V9h2v1a1 1 0 0 0 2 0V9h4v1a1 1 0 0 0 2 0V9h2v9z"/>
      </svg>
    )
  },
  {
    id: "grille",
    name: "GRILLE",
    modType: 6,
    icon: (
      <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
        <path d="M3 6h18v2H3V6zm0 4h18v2H3v-2zm0 4h18v2H3v-2zm0 4h18v2H3v-2z"/>
      </svg>
    )
  },
  {
    id: "hood",
    name: "HOOD",
    modType: 7,
    icon: (
      <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
      </svg>
    )
  },
  {
    id: "fender",
    name: "FENDER",
    modType: 8,
    icon: (
      <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
      </svg>
    )
  },
  {
    id: "suspension",
    name: "SUSPENSION",
    modType: 15,
    icon: (
      <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
        <path d="M12 2l-2 7h4l-2-7zM8 10v4h8v-4H8zm2 6v4h4v-4h-4z"/>
      </svg>
    )
  },
  {
    id: "engine",
    name: "ENGINE",
    modType: 11,
    icon: (
      <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
      </svg>
    )
  },
  {
    id: "brakes",
    name: "BRAKES",
    modType: 12,
    icon: (
      <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"/>
      </svg>
    )
  },
  {
    id: "wheels",
    name: "WHEELS",
    modType: 23,
    icon: (
      <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm-5.5-2.5l7.51-3.49L17.5 6.5 9.99 9.99 6.5 17.5zm5.5-6.6c.61 0 1.1.49 1.1 1.1s-.49 1.1-1.1 1.1-1.1-.49-1.1-1.1.49-1.1 1.1-1.1z"/>
      </svg>
    )
  },
  {
    id: "livery",
    name: "LIVERY",
    modType: 48,
    icon: (
      <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
      </svg>
    )
  }
];

export default function App() {
  const [selectedCategory, setSelectedCategory] = useState(categories[0].id);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Handle mouse wheel scrolling for horizontal scroll
  useEffect(() => {
    const handleWheel = (e: WheelEvent) => {
      if (scrollContainerRef.current) {
        // Prevent default vertical scrolling
        e.preventDefault();

        // Convert vertical scroll to horizontal scroll
        const scrollAmount = e.deltaY * 2; // Multiply for faster scrolling
        scrollContainerRef.current.scrollLeft += scrollAmount;
      }
    };

    const scrollContainer = scrollContainerRef.current;
    if (scrollContainer) {
      scrollContainer.addEventListener('wheel', handleWheel, { passive: false });

      return () => {
        scrollContainer.removeEventListener('wheel', handleWheel);
      };
    }
  }, []);

  return (
    <div 
      className="w-full h-screen text-white overflow-hidden relative"
      style={{
        backgroundImage: "url(/bg.jpg)",
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat"
      }}
    >
      {/* Dark overlay for better text readability */}
      <div className="absolute inset-0 bg-black bg-opacity-30"></div>
      
      {/* Main Content Area - Car will be rendered here by FiveM */}
      <div className="relative w-full h-full flex items-center justify-center">
        {/* This area will be transparent for the 3D car view */}
        <div className="text-center text-gray-500 text-lg">
          {/* Car rendering area - handled by FiveM client */}
        </div>
      </div>

      {/* CUSTOMIZE Label */}
      <div className="absolute z-10" style={{ left: "96px", bottom: "240px" }}>
        <h2 className="text-white text-3xl font-bold tracking-wider drop-shadow-lg">CUSTOMIZE</h2>
      </div>

      {/* Bottom Navigation Bar - FiveM Style Categories */}
      <div className="absolute bottom-0 left-0 right-0 h-48 z-10">
        {/* Background panel */}
        <div className="absolute inset-0 bg-black opacity-90"></div>

        <div
          ref={scrollContainerRef}
          className="relative flex items-center justify-start h-full overflow-x-auto no-scrollbar"
          style={{
            paddingLeft: "96px",
            paddingRight: "96px",
            paddingBottom: "64px",
            paddingTop: "32px",
            gap: "16px"
          }}
        >
          {categories.map((category) => (
            <div
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`flex flex-col items-center justify-center cursor-pointer transition-all duration-300 relative overflow-hidden ${
                selectedCategory === category.id
                  ? "transform scale-105"
                  : "hover:transform hover:scale-102"
              }`}
              style={{
                width: "180px",
                height: "140px",
                flexShrink: 0,
                backgroundColor: selectedCategory === category.id
                  ? "#ffffff"
                  : "#1a1a1a",
                border: selectedCategory === category.id
                  ? "3px solid #ffffff"
                  : "1px solid #404040",
                boxShadow: selectedCategory === category.id
                  ? "0 0 20px rgba(255, 255, 255, 0.3)"
                  : "0 2px 8px rgba(0, 0, 0, 0.5)"
              }}
            >
              {/* Background pattern for unselected */}
              {selectedCategory !== category.id && (
                <div className="absolute inset-0 opacity-5">
                  <div className="w-full h-full bg-gradient-to-br from-white/10 to-transparent"></div>
                </div>
              )}

              {/* Icon container */}
              <div className={`mb-3 transition-all duration-300 ${
                selectedCategory === category.id
                  ? "text-black"
                  : "text-white"
              }`}>
                {category.icon}
              </div>

              {/* Category name */}
              <span className={`text-xs font-bold tracking-widest text-center leading-tight transition-all duration-300 px-2 ${
                selectedCategory === category.id
                  ? "text-black"
                  : "text-white"
              }`}>
                {category.name}
              </span>

              {/* Selection indicator */}
              {selectedCategory === category.id && (
                <div className="absolute top-0 left-0 w-full h-1 bg-black"></div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
