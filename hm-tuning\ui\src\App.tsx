import { useRef, useEffect } from "react";
import { useTuningStore } from "./store/tuningStore";
import { useNUI, useKeyboard } from "./hooks/useNUI";
import ModList from "./components/ModList";
import { VehicleMod } from "./types/tuning";

export default function App() {
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Store state
  const {
    isVisible,
    categories,
    selectedCategory,
    vehicleData,
    loading,
    setSelectedCategory,
    getSelectedCategoryMods
  } = useTuningStore();

  // Hooks
  const { sendNUIMessage } = useNUI();
  useKeyboard();

  // Get current category mods
  const currentMods = getSelectedCategoryMods();

  // Handle mod selection
  const handleModSelect = (mod: VehicleMod) => {
    console.log('[Tuning UI] Selected mod:', mod);

    // Send mod selection to FiveM client
    sendNUIMessage('APPLY_MOD', {
      modType: mod.modType,
      modIndex: mod.modIndex,
      modName: mod.name
    });
  };

  // Handle mouse wheel scrolling for horizontal scroll
  useEffect(() => {
    const handleWheel = (e: WheelEvent) => {
      if (scrollContainerRef.current) {
        // Prevent default vertical scrolling
        e.preventDefault();

        // Convert vertical scroll to horizontal scroll
        const scrollAmount = e.deltaY * 2; // Multiply for faster scrolling
        scrollContainerRef.current.scrollLeft += scrollAmount;
      }
    };

    const scrollContainer = scrollContainerRef.current;
    if (scrollContainer) {
      scrollContainer.addEventListener('wheel', handleWheel, { passive: false });

      return () => {
        scrollContainer.removeEventListener('wheel', handleWheel);
      };
    }
  }, []);

  // Don't render if not visible
  if (!isVisible) {
    return null;
  }

  return (
    <div
      className="w-full h-screen text-white overflow-hidden relative"
      style={{
        backgroundImage: "url(/bg.jpg)",
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat"
      }}
    >
      {/* Dark overlay for better text readability */}
      <div className="absolute inset-0 bg-black bg-opacity-30"></div>

      {/* Main Content Area - Car will be rendered here by FiveM */}
      <div className="relative w-full h-full flex items-center justify-center">
        {/* This area will be transparent for the 3D car view */}
        <div className="text-center text-gray-500 text-lg">
          {/* Car rendering area - handled by FiveM client */}
        </div>
      </div>

      {/* Vehicle Info */}
      {vehicleData && (
        <div className="absolute z-10" style={{ left: "96px", top: "96px" }}>
          <div className="text-white text-lg font-bold tracking-wider drop-shadow-lg">
            {vehicleData.vehicleModel}
          </div>
          <div className="text-gray-300 text-sm">
            {categories.length} modification categories available
          </div>
        </div>
      )}

      {/* CUSTOMIZE Label */}
      <div className="absolute z-10" style={{ left: "96px", bottom: "400px" }}>
        <h2 className="text-white text-3xl font-bold tracking-wider drop-shadow-lg">CUSTOMIZE</h2>
      </div>

      {/* Mod List Area - Shows mods for selected category */}
      {selectedCategory && currentMods.length > 0 && (
        <div className="absolute z-10" style={{
          right: "96px",
          top: "96px",
          bottom: "280px",
          width: "400px"
        }}>
          <div className="bg-black bg-opacity-80 rounded-lg h-full">
            <div className="p-4 border-b border-gray-600">
              <h3 className="text-white text-lg font-bold">
                {categories.find(cat => cat.id === selectedCategory)?.name || 'MODIFICATIONS'}
              </h3>
              <div className="text-gray-400 text-sm">
                {currentMods.length} options available
              </div>
            </div>
            <div className="h-full pb-16">
              <ModList mods={currentMods} onModSelect={handleModSelect} />
            </div>
          </div>
        </div>
      )}

      {/* Loading indicator */}
      {loading && (
        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-20">
          <div className="text-white text-xl">Loading vehicle data...</div>
        </div>
      )}

      {/* Bottom Navigation Bar - Dynamic Categories */}
      <div className="absolute bottom-0 left-0 right-0 h-48 z-10">
        {/* Background panel */}
        <div className="absolute inset-0 bg-black opacity-90"></div>

        {categories.length > 0 ? (
          <div
            ref={scrollContainerRef}
            className="relative flex items-center justify-start h-full overflow-x-auto no-scrollbar"
            style={{
              paddingLeft: "96px",
              paddingRight: "96px",
              paddingBottom: "64px",
              paddingTop: "32px",
              gap: "16px"
            }}
          >
            {categories.map((category) => (
              <div
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`flex flex-col items-center justify-center cursor-pointer transition-all duration-300 relative overflow-hidden ${
                  selectedCategory === category.id
                    ? "transform scale-105"
                    : "hover:transform hover:scale-102"
                }`}
                style={{
                  width: "180px",
                  height: "140px",
                  flexShrink: 0,
                  backgroundColor: selectedCategory === category.id
                    ? "#ffffff"
                    : "#1a1a1a",
                  border: selectedCategory === category.id
                    ? "3px solid #ffffff"
                    : "1px solid #404040",
                  boxShadow: selectedCategory === category.id
                    ? "0 0 20px rgba(255, 255, 255, 0.3)"
                    : "0 2px 8px rgba(0, 0, 0, 0.5)"
                }}
              >
                {/* Background pattern for unselected */}
                {selectedCategory !== category.id && (
                  <div className="absolute inset-0 opacity-5">
                    <div className="w-full h-full bg-gradient-to-br from-white/10 to-transparent"></div>
                  </div>
                )}

                {/* Icon container */}
                <div className={`mb-3 transition-all duration-300 ${
                  selectedCategory === category.id
                    ? "text-black"
                    : "text-white"
                }`}>
                  {category.icon}
                </div>

                {/* Category name */}
                <span className={`text-xs font-bold tracking-widest text-center leading-tight transition-all duration-300 px-2 ${
                  selectedCategory === category.id
                    ? "text-black"
                    : "text-white"
                }`}>
                  {category.name}
                </span>

                {/* Mod count indicator */}
                <div className={`absolute bottom-2 right-2 text-xs px-2 py-1 rounded ${
                  selectedCategory === category.id
                    ? "bg-black text-white"
                    : "bg-white text-black"
                }`}>
                  {category.mods.length}
                </div>

                {/* Selection indicator */}
                {selectedCategory === category.id && (
                  <div className="absolute top-0 left-0 w-full h-1 bg-black"></div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="relative flex items-center justify-center h-full">
            <div className="text-white text-lg">
              {loading ? 'Loading vehicle modifications...' : 'No modifications available for this vehicle'}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
