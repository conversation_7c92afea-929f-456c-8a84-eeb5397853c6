import { useRef, useEffect } from "react";
import { useTuningStore } from "./store/tuningStore";
import { useNUI, useKeyboard } from "./hooks/useNUI";
import ModList from "./components/ModList";
import { VehicleMod, NavigationLevel } from "./types/tuning";

export default function App() {
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Store state
  const {
    isVisible,
    categories,
    selectedCategory,
    selectedSubCategory,
    currentLevel,
    vehicleData,
    loading,
    setVehicleData,
    navigateToSubCategories,
    navigateToMods,
    navigateBack,
    getCurrentItems,
    getSelectedCategory,
    getSelectedSubCategory
  } = useTuningStore();

  // Hooks
  const { sendNUIMessage } = useNUI();
  useKeyboard();

  // Get current items based on navigation level
  const currentItems = getCurrentItems();
  const selectedCategoryData = getSelectedCategory();
  const selectedSubCategoryData = getSelectedSubCategory();

  // Development mode - add mock data
  useEffect(() => {
    if (import.meta.env.DEV && !vehicleData) {
      // Mock vehicle data for development
      const mockData = {
        vehicleModel: "ADDER",
        vehicleHash: 3078201489,
        mods: {
          0: [ // Spoilers
            { modType: 0, modIndex: -1, name: "Stock", category: "body" as const, isInstalled: true, isStock: true },
            { modType: 0, modIndex: 0, name: "Carbon Spoiler", category: "body" as const, isInstalled: false, isStock: false },
            { modType: 0, modIndex: 1, name: "Ducktail Spoiler", category: "body" as const, isInstalled: false, isStock: false }
          ],
          11: [ // Engine
            { modType: 11, modIndex: -1, name: "Stock", category: "performance" as const, isInstalled: false, isStock: true },
            { modType: 11, modIndex: 0, name: "Level 1", category: "performance" as const, isInstalled: false, isStock: false },
            { modType: 11, modIndex: 1, name: "Level 2", category: "performance" as const, isInstalled: true, isStock: false },
            { modType: 11, modIndex: 2, name: "Level 3", category: "performance" as const, isInstalled: false, isStock: false }
          ],
          23: [ // Wheels
            { modType: 23, modIndex: -1, name: "Stock", category: "stance" as const, isInstalled: true, isStock: true },
            { modType: 23, modIndex: 0, name: "Sport Wheels", category: "stance" as const, isInstalled: false, isStock: false }
          ]
        },
        performance: {
          engine: 1,
          brakes: -1,
          transmission: -1,
          suspension: -1,
          armor: -1,
          turbo: false,
          nitrous: false
        }
      };

      setVehicleData(mockData as any);
      console.log('[Tuning UI] Development mode - loaded mock data');
    }
  }, [vehicleData, setVehicleData]);

  // Handle mod selection
  const handleModSelect = (mod: VehicleMod) => {
    console.log('[Tuning UI] Selected mod:', mod);

    // Send mod selection to FiveM client
    sendNUIMessage('APPLY_MOD', {
      modType: mod.modType,
      modIndex: mod.modIndex,
      modName: mod.name
    });
  };

  // Handle mouse wheel scrolling for horizontal scroll
  useEffect(() => {
    const handleWheel = (e: WheelEvent) => {
      if (scrollContainerRef.current) {
        // Prevent default vertical scrolling
        e.preventDefault();

        // Convert vertical scroll to horizontal scroll
        const scrollAmount = e.deltaY * 2; // Multiply for faster scrolling
        scrollContainerRef.current.scrollLeft += scrollAmount;
      }
    };

    const scrollContainer = scrollContainerRef.current;
    if (scrollContainer) {
      scrollContainer.addEventListener('wheel', handleWheel, { passive: false });

      return () => {
        scrollContainer.removeEventListener('wheel', handleWheel);
      };
    }
  }, []);

  // Don't render if not visible (except in development mode)
  const isDevelopment = import.meta.env.DEV;
  if (!isVisible && !isDevelopment) {
    return null;
  }

  return (
    <div
      className="w-full h-screen text-white overflow-hidden relative"
      style={{
        backgroundImage: "url(/bg.jpg)",
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat"
      }}
    >
      {/* Dark overlay for better text readability */}
      <div className="absolute inset-0 bg-black bg-opacity-30"></div>

      {/* Main Content Area - Car will be rendered here by FiveM */}
      <div className="relative w-full h-full flex items-center justify-center">
        {/* This area will be transparent for the 3D car view */}
        <div className="text-center text-gray-500 text-lg">
          {/* Car rendering area - handled by FiveM client */}
        </div>
      </div>

      {/* Vehicle Info */}
      {vehicleData && (
        <div className="absolute z-10" style={{ left: "96px", top: "96px" }}>
          <div className="text-white text-lg font-bold tracking-wider drop-shadow-lg">
            {vehicleData.vehicleModel}
          </div>
          <div className="text-gray-300 text-sm">
            {categories.length} modification categories available
          </div>
        </div>
      )}

      {/* CUSTOMIZE Label */}
      <div className="absolute z-10" style={{ left: "96px", bottom: "400px" }}>
        <h2 className="text-white text-3xl font-bold tracking-wider drop-shadow-lg">CUSTOMIZE</h2>
      </div>

      {/* Navigation Breadcrumb */}
      {(selectedCategoryData || selectedSubCategoryData) && (
        <div className="absolute z-10" style={{ left: "96px", bottom: "350px" }}>
          <div className="text-white text-sm opacity-75">
            {selectedCategoryData && (
              <>
                <span>{selectedCategoryData.name}</span>
                {selectedSubCategoryData && (
                  <>
                    <span className="mx-2">›</span>
                    <span>{selectedSubCategoryData.name}</span>
                  </>
                )}
              </>
            )}
          </div>
        </div>
      )}

      {/* Mod List Area - Shows mods when at mod level */}
      {currentLevel === NavigationLevel.MODS && currentItems.length > 0 && (
        <div className="absolute z-10" style={{
          right: "96px",
          top: "96px",
          bottom: "280px",
          width: "400px"
        }}>
          <div className="bg-black bg-opacity-80 rounded-lg h-full">
            <div className="p-4 border-b border-gray-600">
              <h3 className="text-white text-lg font-bold">
                {selectedSubCategoryData?.name || 'MODIFICATIONS'}
              </h3>
              <div className="text-gray-400 text-sm">
                {currentItems.length} options available
              </div>
            </div>
            <div className="h-full pb-16">
              <ModList mods={currentItems as VehicleMod[]} onModSelect={handleModSelect} />
            </div>
          </div>
        </div>
      )}

      {/* Loading indicator */}
      {loading && (
        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-20">
          <div className="text-white text-xl">Loading vehicle data...</div>
        </div>
      )}

      {/* Bottom Navigation Bar - Hierarchical Navigation */}
      <div className="absolute bottom-0 left-0 right-0 h-48 z-10">
        {/* Background panel */}
        <div className="absolute inset-0 bg-black opacity-90"></div>

        {currentItems.length > 0 ? (
          <div
            ref={scrollContainerRef}
            className="relative flex items-center justify-start h-full overflow-x-auto no-scrollbar"
            style={{
              paddingLeft: "96px",
              paddingRight: "96px",
              paddingBottom: "64px",
              paddingTop: "32px",
              gap: "16px"
            }}
          >
            {/* Back button */}
            {(selectedCategoryData || selectedSubCategoryData) && (
              <div
                onClick={navigateBack}
                className="flex flex-col items-center justify-center cursor-pointer transition-all duration-300 relative overflow-hidden hover:transform hover:scale-102"
                style={{
                  width: "140px",
                  height: "140px",
                  flexShrink: 0,
                  backgroundColor: "#333333",
                  border: "1px solid #666666",
                  boxShadow: "0 2px 8px rgba(0, 0, 0, 0.5)"
                }}
              >
                <div className="mb-3 text-white">
                  <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.42-1.41L7.83 13H20v-2z"/>
                  </svg>
                </div>
                <span className="text-xs font-bold tracking-widest text-center leading-tight text-white px-2">
                  BACK
                </span>
              </div>
            )}

            {/* Current level items */}
            {currentItems.map((item: any) => {
              const isMainCategory = 'subCategories' in item;
              const isSubCategory = 'modType' in item;
              const isMod = 'isInstalled' in item;

              const handleClick = () => {
                if (isMainCategory) {
                  navigateToSubCategories(item.id);
                } else if (isSubCategory) {
                  navigateToMods(item.id);
                } else if (isMod) {
                  handleModSelect(item);
                }
              };

              const isSelected = isMainCategory
                ? selectedCategory === item.id
                : isSubCategory
                  ? selectedSubCategory === item.id
                  : item.isInstalled;

              return (
                <div
                  key={item.id || `${item.modType}-${item.modIndex}`}
                  onClick={handleClick}
                  className={`flex flex-col items-center justify-center cursor-pointer transition-all duration-300 relative overflow-hidden ${
                    isSelected
                      ? "transform scale-105"
                      : "hover:transform hover:scale-102"
                  }`}
                  style={{
                    width: "180px",
                    height: "140px",
                    flexShrink: 0,
                    backgroundColor: isSelected
                      ? "#ffffff"
                      : "#1a1a1a",
                    border: isSelected
                      ? "3px solid #ffffff"
                      : "1px solid #404040",
                    boxShadow: isSelected
                      ? "0 0 20px rgba(255, 255, 255, 0.3)"
                      : "0 2px 8px rgba(0, 0, 0, 0.5)"
                  }}
                >
                  {/* Background pattern for unselected */}
                  {!isSelected && (
                    <div className="absolute inset-0 opacity-5">
                      <div className="w-full h-full bg-gradient-to-br from-white/10 to-transparent"></div>
                    </div>
                  )}

                  {/* Icon/Content */}
                  <div className={`mb-3 transition-all duration-300 ${
                    isSelected ? "text-black" : "text-white"
                  }`}>
                    {isMainCategory ? (
                      item.icon
                    ) : isMod ? (
                      <div className="text-2xl">
                        {item.isStock ? "📦" : item.isInstalled ? "✅" : "🔧"}
                      </div>
                    ) : (
                      <div className="text-2xl">⚙️</div>
                    )}
                  </div>

                  {/* Name */}
                  <span className={`text-xs font-bold tracking-widest text-center leading-tight transition-all duration-300 px-2 ${
                    isSelected ? "text-black" : "text-white"
                  }`}>
                    {item.name}
                  </span>

                  {/* Count indicator */}
                  {(isMainCategory || isSubCategory) && (
                    <div className={`absolute bottom-2 right-2 text-xs px-2 py-1 rounded ${
                      isSelected
                        ? "bg-black text-white"
                        : "bg-white text-black"
                    }`}>
                      {isMainCategory ? item.subCategories.length : item.mods.length}
                    </div>
                  )}

                  {/* Selection indicator */}
                  {isSelected && (
                    <div className="absolute top-0 left-0 w-full h-1 bg-black"></div>
                  )}
                </div>
              );
            })}
          </div>
        ) : (
          <div className="relative flex items-center justify-center h-full">
            <div className="text-white text-lg">
              {loading ? 'Loading vehicle modifications...' : 'No modifications available for this vehicle'}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
