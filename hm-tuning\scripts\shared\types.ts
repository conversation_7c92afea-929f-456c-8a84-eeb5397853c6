// Vehicle modification types from GTA V/FiveM
export enum VehicleModType {
    SPOILER = 0,
    BUMPER_F = 1,
    BUMPER_R = 2,
    SKIRT = 3,
    EXHAUST = 4,
    CHASSIS = 5,
    GRILL = 6,
    BONNET = 7,
    WING_L = 8,
    WING_R = 9,
    ROOF = 10,
    ENGINE = 11,
    BRAKES = 12,
    GEARBOX = 13,
    HORN = 14,
    SUSPENSION = 15,
    ARMOUR = 16,
    NITROUS = 17,
    TURBO = 18,
    SUBWOOFER = 19,
    TYRE_SMOKE = 20,
    HYDRAULICS = 21,
    XENON_LIGHTS = 22,
    WHEELS = 23,
    WHEELS_REAR_OR_HYDRAULICS = 24,
    PLTHOLDER = 25,
    PLTVANITY = 26,
    INTERIOR1 = 27,
    INTERIOR2 = 28,
    INTERIOR3 = 29,
    INTERIOR4 = 30,
    INTERIOR5 = 31,
    SEATS = 32,
    STEERING = 33,
    KNOB = 34,
    PLAQUE = 35,
    ICE = 36,
    TRUNK = 37,
    HYDRO = 38,
    ENGINEBAY1 = 39,
    ENGINEBAY2 = 40,
    ENGINEBAY3 = 41,
    CHASSIS2 = 42,
    CHASSIS3 = 43,
    CHASSIS4 = 44,
    CHASSIS5 = 45,
    DOOR_L = 46,
    DOOR_R = 47,
    LIVERY_MOD = 48,
    LIGHTBAR = 49
}

// Categories for organizing mods in UI
export enum ModCategory {
    BODY = 'body',
    PERFORMANCE = 'performance',
    EFFECTS = 'effects',
    STANCE = 'stance',
    INTERIOR = 'interior'
}

// Mod information structure
export interface VehicleMod {
    modType: VehicleModType;
    modIndex: number;
    name: string;
    category: ModCategory;
    isInstalled: boolean;
    isStock: boolean;
}

// Available mods for a vehicle
export interface VehicleModData {
    vehicleModel: string;
    vehicleHash: number;
    mods: Record<VehicleModType, VehicleMod[]>;
    performance: {
        engine: number;
        brakes: number;
        transmission: number;
        suspension: number;
        armor: number;
        turbo: boolean;
        nitrous: boolean;
    };
}

// Mod category mappings
export const MOD_CATEGORIES: Record<VehicleModType, ModCategory> = {
    [VehicleModType.SPOILER]: ModCategory.BODY,
    [VehicleModType.BUMPER_F]: ModCategory.BODY,
    [VehicleModType.BUMPER_R]: ModCategory.BODY,
    [VehicleModType.SKIRT]: ModCategory.BODY,
    [VehicleModType.EXHAUST]: ModCategory.BODY,
    [VehicleModType.CHASSIS]: ModCategory.BODY,
    [VehicleModType.GRILL]: ModCategory.BODY,
    [VehicleModType.BONNET]: ModCategory.BODY,
    [VehicleModType.WING_L]: ModCategory.BODY,
    [VehicleModType.WING_R]: ModCategory.BODY,
    [VehicleModType.ROOF]: ModCategory.BODY,
    [VehicleModType.ENGINE]: ModCategory.PERFORMANCE,
    [VehicleModType.BRAKES]: ModCategory.PERFORMANCE,
    [VehicleModType.GEARBOX]: ModCategory.PERFORMANCE,
    [VehicleModType.SUSPENSION]: ModCategory.PERFORMANCE,
    [VehicleModType.ARMOUR]: ModCategory.PERFORMANCE,
    [VehicleModType.NITROUS]: ModCategory.PERFORMANCE,
    [VehicleModType.TURBO]: ModCategory.PERFORMANCE,
    [VehicleModType.HORN]: ModCategory.EFFECTS,
    [VehicleModType.SUBWOOFER]: ModCategory.EFFECTS,
    [VehicleModType.TYRE_SMOKE]: ModCategory.EFFECTS,
    [VehicleModType.HYDRAULICS]: ModCategory.EFFECTS,
    [VehicleModType.XENON_LIGHTS]: ModCategory.EFFECTS,
    [VehicleModType.WHEELS]: ModCategory.STANCE,
    [VehicleModType.WHEELS_REAR_OR_HYDRAULICS]: ModCategory.STANCE,
    [VehicleModType.PLTHOLDER]: ModCategory.BODY,
    [VehicleModType.PLTVANITY]: ModCategory.BODY,
    [VehicleModType.INTERIOR1]: ModCategory.INTERIOR,
    [VehicleModType.INTERIOR2]: ModCategory.INTERIOR,
    [VehicleModType.INTERIOR3]: ModCategory.INTERIOR,
    [VehicleModType.INTERIOR4]: ModCategory.INTERIOR,
    [VehicleModType.INTERIOR5]: ModCategory.INTERIOR,
    [VehicleModType.SEATS]: ModCategory.INTERIOR,
    [VehicleModType.STEERING]: ModCategory.INTERIOR,
    [VehicleModType.KNOB]: ModCategory.INTERIOR,
    [VehicleModType.PLAQUE]: ModCategory.INTERIOR,
    [VehicleModType.ICE]: ModCategory.INTERIOR,
    [VehicleModType.TRUNK]: ModCategory.INTERIOR,
    [VehicleModType.HYDRO]: ModCategory.EFFECTS,
    [VehicleModType.ENGINEBAY1]: ModCategory.BODY,
    [VehicleModType.ENGINEBAY2]: ModCategory.BODY,
    [VehicleModType.ENGINEBAY3]: ModCategory.BODY,
    [VehicleModType.CHASSIS2]: ModCategory.BODY,
    [VehicleModType.CHASSIS3]: ModCategory.BODY,
    [VehicleModType.CHASSIS4]: ModCategory.BODY,
    [VehicleModType.CHASSIS5]: ModCategory.BODY,
    [VehicleModType.DOOR_L]: ModCategory.BODY,
    [VehicleModType.DOOR_R]: ModCategory.BODY,
    [VehicleModType.LIVERY_MOD]: ModCategory.BODY,
    [VehicleModType.LIGHTBAR]: ModCategory.EFFECTS
};

// Performance mod types for easy filtering
export const PERFORMANCE_MODS = [
    VehicleModType.ENGINE,
    VehicleModType.BRAKES,
    VehicleModType.GEARBOX,
    VehicleModType.SUSPENSION,
    VehicleModType.ARMOUR,
    VehicleModType.NITROUS,
    VehicleModType.TURBO
];

// UI-specific types for hierarchical navigation
export interface UICategory {
    id: string;
    name: string;
    category: ModCategory;
    icon?: any; // Optional for client-side, will be added in UI
    subCategories: UISubCategory[];
}

// Sub-category within a main category
export interface UISubCategory {
    id: string;
    name: string;
    modType: VehicleModType;
    mods: VehicleMod[];
}

// Navigation state
export enum NavigationLevel {
    MAIN_CATEGORIES = 'main_categories',
    SUB_CATEGORIES = 'sub_categories',
    MODS = 'mods'
}

// Tuning state interface
export interface TuningState {
    isVisible: boolean;
    vehicleData: VehicleModData | null;
    categories: UICategory[];
    selectedCategory: string | null;
    selectedSubCategory: string | null;
    currentLevel: NavigationLevel;
    loading: boolean;
}

// FiveM NUI message types
export interface NUIMessage {
    type: string;
    data: any;
}