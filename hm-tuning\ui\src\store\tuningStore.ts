import { create } from 'zustand';
import { VehicleModData, UICategory, ModCategory, TuningState, VehicleMod } from '../types/tuning';
import { getCategoryIcon, getCategoryDisplayName } from '../components/CategoryIcons';

// Generate UI categories from vehicle mod data
const generateUICategories = (vehicleData: VehicleModData): UICategory[] => {
  const categorizedMods: Record<ModCategory, VehicleMod[]> = {
    [ModCategory.BODY]: [],
    [ModCategory.PERFORMANCE]: [],
    [ModCategory.EFFECTS]: [],
    [ModCategory.STANCE]: [],
    [ModCategory.INTERIOR]: []
  };

  // Organize mods by category
  Object.values(vehicleData.mods).forEach(modArray => {
    modArray.forEach(mod => {
      categorizedMods[mod.category].push(mod);
    });
  });

  // Create UI categories only for categories that have mods
  const uiCategories: UICategory[] = [];
  
  Object.entries(categorizedMods).forEach(([category, mods]) => {
    if (mods.length > 0) {
      uiCategories.push({
        id: category,
        name: getCategoryDisplayName(category as ModCategory),
        category: category as ModCategory,
        icon: getCategoryIcon(category as ModCategory),
        mods: mods
      });
    }
  });

  return uiCategories;
};

interface TuningStore extends TuningState {
  // Actions
  setVisible: (visible: boolean) => void;
  setVehicleData: (data: VehicleModData | null) => void;
  setSelectedCategory: (categoryId: string | null) => void;
  setLoading: (loading: boolean) => void;
  
  // Computed
  getSelectedCategoryMods: () => VehicleMod[];
  getInstalledMods: () => VehicleMod[];
  
  // NUI handlers
  handleNUIMessage: (message: any) => void;
}

export const useTuningStore = create<TuningStore>((set, get) => ({
  // Initial state
  isVisible: false,
  vehicleData: null,
  categories: [],
  selectedCategory: null,
  loading: false,

  // Actions
  setVisible: (visible: boolean) => {
    set({ isVisible: visible });
    if (!visible) {
      // Reset state when hiding
      set({ 
        vehicleData: null, 
        categories: [], 
        selectedCategory: null,
        loading: false 
      });
    }
  },

  setVehicleData: (data: VehicleModData | null) => {
    if (data) {
      const categories = generateUICategories(data);
      const selectedCategory = categories.length > 0 ? categories[0].id : null;
      
      set({ 
        vehicleData: data, 
        categories,
        selectedCategory,
        loading: false
      });
    } else {
      set({ 
        vehicleData: null, 
        categories: [], 
        selectedCategory: null 
      });
    }
  },

  setSelectedCategory: (categoryId: string | null) => {
    set({ selectedCategory: categoryId });
  },

  setLoading: (loading: boolean) => {
    set({ loading });
  },

  // Computed getters
  getSelectedCategoryMods: () => {
    const { categories, selectedCategory } = get();
    const category = categories.find(cat => cat.id === selectedCategory);
    return category?.mods || [];
  },

  getInstalledMods: () => {
    const { vehicleData } = get();
    if (!vehicleData) return [];
    
    const installedMods: VehicleMod[] = [];
    Object.values(vehicleData.mods).forEach(modArray => {
      const installed = modArray.find(mod => mod.isInstalled);
      if (installed && !installed.isStock) {
        installedMods.push(installed);
      }
    });
    
    return installedMods;
  },

  // NUI message handler
  handleNUIMessage: (message: any) => {
    const { type, data } = message;
    
    switch (type) {
      case 'SHOW_TUNING_UI':
        set({ isVisible: true, loading: true });
        break;
        
      case 'HIDE_TUNING_UI':
        get().setVisible(false);
        break;
        
      case 'UPDATE_VEHICLE_DATA':
        get().setVehicleData(data);
        break;
        
      case 'SET_LOADING':
        get().setLoading(data.loading);
        break;

      case 'TEST_MESSAGE':
        console.log('[Tuning UI] Test message received:', data);
        alert('NUI Communication Working! Message: ' + JSON.stringify(data));
        break;

      default:
        console.log('[Tuning UI] Unknown message type:', type);
    }
  }
}));
