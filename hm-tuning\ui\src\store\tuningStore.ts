import { create } from 'zustand';
import { VehicleModData, UICategory, UISubCategory, ModCategory, TuningState, VehicleMod, NavigationLevel, VehicleModType } from '../types/tuning';
import { getCategoryIcon, getCategoryDisplayName } from '../components/CategoryIcons';

// Mod type display names
const getModTypeDisplayName = (modType: VehicleModType): string => {
  const modTypeNames: Record<VehicleModType, string> = {
    [VehicleModType.SPOILER]: 'SPOILERS',
    [VehicleModType.BUMPER_F]: 'FRONT BUMPER',
    [VehicleModType.BUMPER_R]: 'REAR BUMPER',
    [VehicleModType.SKIRT]: 'SIDE SKIRTS',
    [VehicleModType.EXHAUST]: 'EXHAUST',
    [VehicleModType.CHASSIS]: 'ROLL CAGE',
    [VehicleModType.GRILL]: 'GRILLE',
    [VehicleModType.BONNET]: 'HOOD',
    [VehicleModType.WING_L]: 'LEFT FENDER',
    [VehicleModType.WING_R]: 'RIGHT FENDER',
    [VehicleModType.ROOF]: 'ROOF',
    [VehicleModType.ENGINE]: 'ENGINE',
    [VehicleModType.BRAKES]: 'BRAKES',
    [VehicleModType.GEARBOX]: 'TRANSMISSION',
    [VehicleModType.HORN]: 'HORN',
    [VehicleModType.SUSPENSION]: 'SUSPENSION',
    [VehicleModType.ARMOUR]: 'ARMOR',
    [VehicleModType.NITROUS]: 'NITROUS',
    [VehicleModType.TURBO]: 'TURBO',
    [VehicleModType.SUBWOOFER]: 'SUBWOOFER',
    [VehicleModType.TYRE_SMOKE]: 'TIRE SMOKE',
    [VehicleModType.HYDRAULICS]: 'HYDRAULICS',
    [VehicleModType.XENON_LIGHTS]: 'XENON LIGHTS',
    [VehicleModType.WHEELS]: 'WHEELS',
    [VehicleModType.WHEELS_REAR_OR_HYDRAULICS]: 'REAR WHEELS',
    [VehicleModType.PLTHOLDER]: 'PLATE HOLDER',
    [VehicleModType.PLTVANITY]: 'VANITY PLATES',
    [VehicleModType.INTERIOR1]: 'INTERIOR 1',
    [VehicleModType.INTERIOR2]: 'INTERIOR 2',
    [VehicleModType.INTERIOR3]: 'INTERIOR 3',
    [VehicleModType.INTERIOR4]: 'INTERIOR 4',
    [VehicleModType.INTERIOR5]: 'INTERIOR 5',
    [VehicleModType.SEATS]: 'SEATS',
    [VehicleModType.STEERING]: 'STEERING WHEEL',
    [VehicleModType.KNOB]: 'SHIFT KNOB',
    [VehicleModType.PLAQUE]: 'PLAQUES',
    [VehicleModType.ICE]: 'ICE',
    [VehicleModType.TRUNK]: 'TRUNK',
    [VehicleModType.HYDRO]: 'HYDRAULICS',
    [VehicleModType.ENGINEBAY1]: 'ENGINE BAY 1',
    [VehicleModType.ENGINEBAY2]: 'ENGINE BAY 2',
    [VehicleModType.ENGINEBAY3]: 'ENGINE BAY 3',
    [VehicleModType.CHASSIS2]: 'CHASSIS 2',
    [VehicleModType.CHASSIS3]: 'CHASSIS 3',
    [VehicleModType.CHASSIS4]: 'CHASSIS 4',
    [VehicleModType.CHASSIS5]: 'CHASSIS 5',
    [VehicleModType.DOOR_L]: 'LEFT DOOR',
    [VehicleModType.DOOR_R]: 'RIGHT DOOR',
    [VehicleModType.LIVERY_MOD]: 'LIVERY',
    [VehicleModType.LIGHTBAR]: 'LIGHT BAR'
  };

  return modTypeNames[modType] || `MOD TYPE ${modType}`;
};

// Generate UI categories from vehicle mod data
const generateUICategories = (vehicleData: VehicleModData): UICategory[] => {
  const categorizedMods: Record<ModCategory, Partial<Record<VehicleModType, VehicleMod[]>>> = {
    [ModCategory.BODY]: {},
    [ModCategory.PERFORMANCE]: {},
    [ModCategory.EFFECTS]: {},
    [ModCategory.STANCE]: {},
    [ModCategory.INTERIOR]: {}
  };

  // Organize mods by category and mod type
  Object.entries(vehicleData.mods).forEach(([modTypeStr, modArray]) => {
    const modType = parseInt(modTypeStr) as VehicleModType;
    modArray.forEach(mod => {
      if (!categorizedMods[mod.category][modType]) {
        categorizedMods[mod.category][modType] = [];
      }
      categorizedMods[mod.category][modType]!.push(mod);
    });
  });

  // Create UI categories only for categories that have mods
  const uiCategories: UICategory[] = [];

  Object.entries(categorizedMods).forEach(([category, modTypeGroups]) => {
    const subCategories: UISubCategory[] = [];

    Object.entries(modTypeGroups).forEach(([modTypeStr, mods]) => {
      const modType = parseInt(modTypeStr) as VehicleModType;
      if (mods.length > 0) {
        subCategories.push({
          id: `${category}_${modType}`,
          name: getModTypeDisplayName(modType),
          modType: modType,
          mods: mods
        });
      }
    });

    if (subCategories.length > 0) {
      uiCategories.push({
        id: category,
        name: getCategoryDisplayName(category as ModCategory),
        category: category as ModCategory,
        icon: getCategoryIcon(category as ModCategory),
        subCategories: subCategories
      });
    }
  });

  return uiCategories;
};

interface TuningStore extends TuningState {
  // Actions
  setVisible: (visible: boolean) => void;
  setVehicleData: (data: VehicleModData | null) => void;
  setSelectedCategory: (categoryId: string | null) => void;
  setSelectedSubCategory: (subCategoryId: string | null) => void;
  setCurrentLevel: (level: NavigationLevel) => void;
  setLoading: (loading: boolean) => void;

  // Navigation
  navigateToSubCategories: (categoryId: string) => void;
  navigateToMods: (subCategoryId: string) => void;
  navigateBack: () => void;

  // Computed
  getCurrentItems: () => UICategory[] | UISubCategory[] | VehicleMod[];
  getSelectedCategory: () => UICategory | null;
  getSelectedSubCategory: () => UISubCategory | null;
  getInstalledMods: () => VehicleMod[];

  // NUI handlers
  handleNUIMessage: (message: any) => void;
}

export const useTuningStore = create<TuningStore>((set, get) => ({
  // Initial state
  isVisible: false,
  vehicleData: null,
  categories: [],
  selectedCategory: null,
  selectedSubCategory: null,
  currentLevel: NavigationLevel.MAIN_CATEGORIES,
  loading: false,

  // Actions
  setVisible: (visible: boolean) => {
    set({ isVisible: visible });
    if (!visible) {
      // Reset state when hiding
      set({
        vehicleData: null,
        categories: [],
        selectedCategory: null,
        selectedSubCategory: null,
        currentLevel: NavigationLevel.MAIN_CATEGORIES,
        loading: false
      });
    }
  },

  setVehicleData: (data: VehicleModData | null) => {
    if (data) {
      const categories = generateUICategories(data);

      set({
        vehicleData: data,
        categories,
        selectedCategory: null,
        selectedSubCategory: null,
        currentLevel: NavigationLevel.MAIN_CATEGORIES,
        loading: false
      });
    } else {
      set({
        vehicleData: null,
        categories: [],
        selectedCategory: null,
        selectedSubCategory: null,
        currentLevel: NavigationLevel.MAIN_CATEGORIES
      });
    }
  },

  setSelectedCategory: (categoryId: string | null) => {
    set({ selectedCategory: categoryId });
  },

  setSelectedSubCategory: (subCategoryId: string | null) => {
    set({ selectedSubCategory: subCategoryId });
  },

  setCurrentLevel: (level: NavigationLevel) => {
    set({ currentLevel: level });
  },

  setLoading: (loading: boolean) => {
    set({ loading });
  },

  // Navigation methods
  navigateToSubCategories: (categoryId: string) => {
    set({
      selectedCategory: categoryId,
      selectedSubCategory: null,
      currentLevel: NavigationLevel.SUB_CATEGORIES
    });
  },

  navigateToMods: (subCategoryId: string) => {
    set({
      selectedSubCategory: subCategoryId,
      currentLevel: NavigationLevel.MODS
    });
  },

  navigateBack: () => {
    const { currentLevel } = get();

    if (currentLevel === NavigationLevel.MODS) {
      set({
        selectedSubCategory: null,
        currentLevel: NavigationLevel.SUB_CATEGORIES
      });
    } else if (currentLevel === NavigationLevel.SUB_CATEGORIES) {
      set({
        selectedCategory: null,
        selectedSubCategory: null,
        currentLevel: NavigationLevel.MAIN_CATEGORIES
      });
    }
  },

  // Computed getters
  getCurrentItems: () => {
    const { currentLevel, categories, selectedCategory, selectedSubCategory } = get();

    switch (currentLevel) {
      case NavigationLevel.MAIN_CATEGORIES:
        return categories;

      case NavigationLevel.SUB_CATEGORIES:
        const category = categories.find(cat => cat.id === selectedCategory);
        return category?.subCategories || [];

      case NavigationLevel.MODS:
        const selectedCat = categories.find(cat => cat.id === selectedCategory);
        const subCategory = selectedCat?.subCategories.find(sub => sub.id === selectedSubCategory);
        return subCategory?.mods || [];

      default:
        return [];
    }
  },

  getSelectedCategory: () => {
    const { categories, selectedCategory } = get();
    return categories.find(cat => cat.id === selectedCategory) || null;
  },

  getSelectedSubCategory: () => {
    const { categories, selectedCategory, selectedSubCategory } = get();
    const category = categories.find(cat => cat.id === selectedCategory);
    return category?.subCategories.find(sub => sub.id === selectedSubCategory) || null;
  },

  getInstalledMods: () => {
    const { vehicleData } = get();
    if (!vehicleData) return [];

    const installedMods: VehicleMod[] = [];
    Object.values(vehicleData.mods).forEach(modArray => {
      const installed = modArray.find(mod => mod.isInstalled);
      if (installed && !installed.isStock) {
        installedMods.push(installed);
      }
    });

    return installedMods;
  },

  // NUI message handler
  handleNUIMessage: (message: any) => {
    const { type, data } = message;
    
    switch (type) {
      case 'SHOW_TUNING_UI':
        set({ isVisible: true, loading: true });
        break;
        
      case 'HIDE_TUNING_UI':
        get().setVisible(false);
        break;
        
      case 'UPDATE_VEHICLE_DATA':
        get().setVehicleData(data);
        break;
        
      case 'SET_LOADING':
        get().setLoading(data.loading);
        break;

      case 'TEST_MESSAGE':
        console.log('[Tuning UI] Test message received:', data);
        alert('NUI Communication Working! Message: ' + JSON.stringify(data));
        break;

      default:
        console.log('[Tuning UI] Unknown message type:', type);
    }
  }
}));
